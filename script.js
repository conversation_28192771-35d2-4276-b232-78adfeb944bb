// Mobile Navigation Toggle
document.addEventListener('DOMContentLoaded', function() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    const modal = document.getElementById('fishModal');
    const closeModal = document.querySelector('.close');

    // Mobile menu toggle
    hamburger.addEventListener('click', function() {
        hamburger.classList.toggle('active');
        navMenu.classList.toggle('active');
    });

    // Close mobile menu when clicking on a link
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', () => {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });

    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Modal functionality
    closeModal.addEventListener('click', function() {
        modal.style.display = 'none';
    });

    window.addEventListener('click', function(event) {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });

    // Header scroll effect
    window.addEventListener('scroll', function() {
        const header = document.querySelector('.header');
        if (window.scrollY > 100) {
            header.style.background = 'rgba(255, 255, 255, 0.98)';
        } else {
            header.style.background = 'rgba(255, 255, 255, 0.95)';
        }
    });

    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe fish cards for animation
    document.querySelectorAll('.fish-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });

    // Observe education cards for animation
    document.querySelectorAll('.education-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
});

// Fish details data
const fishData = {
    tilapia: {
        name: "Tilapia",
        scientificName: "Oreochromis niloticus",
        habitat: "Freshwater lakes, rivers, and ponds",
        diet: "Omnivorous - algae, plants, small fish, and insects",
        size: "15-30 cm (6-12 inches)",
        lifespan: "6-8 years",
        nutrition: "High in protein, low in fat, rich in phosphorus and selenium",
        facts: [
            "Originally from Africa, now farmed worldwide",
            "Can tolerate a wide range of water conditions",
            "One of the most important aquaculture species globally",
            "Fast-growing and disease-resistant"
        ],
        conservation: "Least Concern - widely farmed and sustainable when properly managed"
    },
    milkfish: {
        name: "Milkfish (Bangus)",
        scientificName: "Chanos chanos",
        habitat: "Coastal waters, estuaries, and freshwater",
        diet: "Herbivorous - algae, soft plants, and small invertebrates",
        size: "100-180 cm (3-6 feet)",
        lifespan: "15+ years",
        nutrition: "High in protein, omega-3 fatty acids, and vitamin B12",
        facts: [
            "National fish of the Philippines",
            "Can live in both saltwater and freshwater",
            "Important in Southeast Asian aquaculture",
            "Has a silvery, streamlined body"
        ],
        conservation: "Least Concern - sustainably farmed in many regions"
    },
    "lapu-lapu": {
        name: "Lapu-Lapu (Grouper)",
        scientificName: "Epinephelus spp.",
        habitat: "Coral reefs and rocky coastal areas",
        diet: "Carnivorous - fish, crustaceans, and cephalopods",
        size: "30-150 cm depending on species",
        lifespan: "10-40+ years",
        nutrition: "Excellent source of lean protein, low in mercury",
        facts: [
            "Named after Filipino hero Lapu-Lapu",
            "Prized for their firm, white meat",
            "Important in reef ecosystems as predators",
            "Some species can change sex during their lifetime"
        ],
        conservation: "Varies by species - some are overfished and need protection"
    },
    dalag: {
        name: "Dalag (Snakehead)",
        scientificName: "Channa striata",
        habitat: "Freshwater swamps, rivers, and rice fields",
        diet: "Carnivorous - fish, frogs, and aquatic insects",
        size: "25-100 cm (10-40 inches)",
        lifespan: "8-10 years",
        nutrition: "High in protein, low in fat, rich in amino acids",
        facts: [
            "Can breathe air and survive out of water for hours",
            "Important food fish in Southeast Asia",
            "Has healing properties in traditional medicine",
            "Aggressive predator with strong parental care"
        ],
        conservation: "Least Concern - adaptable and widely distributed"
    },
    tuna: {
        name: "Tuna",
        scientificName: "Thunnus spp.",
        habitat: "Open ocean waters worldwide",
        diet: "Carnivorous - fish, squid, and crustaceans",
        size: "30-300 cm depending on species",
        lifespan: "15-50+ years",
        nutrition: "Extremely high in protein, omega-3s, and vitamin D",
        facts: [
            "Can swim at speeds up to 75 km/h (47 mph)",
            "Warm-blooded fish with excellent vision",
            "Highly migratory, traveling thousands of miles",
            "Bluefin tuna is one of the most valuable fish"
        ],
        conservation: "Varies - some species are endangered due to overfishing"
    },
    mackerel: {
        name: "Mackerel",
        scientificName: "Scomber scombrus",
        habitat: "Coastal and offshore waters",
        diet: "Carnivorous - small fish, zooplankton, and crustaceans",
        size: "25-60 cm (10-24 inches)",
        lifespan: "17-20 years",
        nutrition: "Very high in omega-3 fatty acids, vitamin B12, and selenium",
        facts: [
            "Distinctive dark wavy stripes on back",
            "Forms large schools for protection",
            "Important commercial fish species",
            "Excellent source of healthy fats"
        ],
        conservation: "Generally stable but some populations face pressure"
    },
    catfish: {
        name: "Catfish (Hito)",
        scientificName: "Clarias batrachus",
        habitat: "Freshwater rivers, ponds, and rice fields",
        diet: "Omnivorous - plants, insects, small fish, and detritus",
        size: "20-45 cm (8-18 inches)",
        lifespan: "8-20 years",
        nutrition: "Good source of protein, vitamin B12, and phosphorus",
        facts: [
            "Has whisker-like barbels for sensing food",
            "Can survive in low-oxygen environments",
            "Important aquaculture species in Asia",
            "Scaleless with smooth, slimy skin"
        ],
        conservation: "Least Concern - widely farmed and adaptable"
    },
    dilis: {
        name: "Dilis (Anchovies)",
        scientificName: "Stolephorus spp.",
        habitat: "Coastal marine waters",
        diet: "Planktivorous - zooplankton and small organisms",
        size: "5-15 cm (2-6 inches)",
        lifespan: "2-4 years",
        nutrition: "High in protein, calcium, and omega-3 fatty acids",
        facts: [
            "Form massive schools for protection",
            "Important base of marine food webs",
            "Used to make fish sauce and dried fish",
            "Small but nutritionally dense"
        ],
        conservation: "Generally stable but sensitive to environmental changes"
    },
    pufferfish: {
        name: "Pufferfish",
        scientificName: "Tetraodontidae",
        habitat: "Tropical and subtropical waters",
        diet: "Omnivorous - algae, small invertebrates, and coral",
        size: "2.5-60 cm depending on species",
        lifespan: "4-8 years",
        nutrition: "Contains tetrodotoxin - potentially dangerous if not prepared properly",
        facts: [
            "Can inflate body to 2-3 times normal size",
            "Most species are highly toxic",
            "Considered a delicacy (fugu) in Japan",
            "Have beak-like teeth for crushing shells"
        ],
        conservation: "Varies by species - some face habitat loss"
    },
    salmon: {
        name: "Salmon",
        scientificName: "Salmo salar",
        habitat: "Both freshwater and marine environments",
        diet: "Carnivorous - insects, small fish, and crustaceans",
        size: "60-150 cm (2-5 feet)",
        lifespan: "2-8 years",
        nutrition: "Excellent source of omega-3s, protein, and vitamin D",
        facts: [
            "Anadromous - born in freshwater, mature in ocean",
            "Return to birthplace to spawn",
            "Important culturally and economically",
            "Pink flesh comes from eating krill and shrimp"
        ],
        conservation: "Varies - wild populations declining, farmed widely"
    }
};

// Show fish details in modal
function showFishDetails(fishType) {
    const fish = fishData[fishType];
    if (!fish) return;

    const modalContent = document.getElementById('modalContent');
    modalContent.innerHTML = `
        <h2>${fish.name}</h2>
        <p class="scientific-name"><em>${fish.scientificName}</em></p>
        
        <div class="fish-detail-section">
            <h3>🏠 Habitat</h3>
            <p>${fish.habitat}</p>
        </div>
        
        <div class="fish-detail-section">
            <h3>🍽️ Diet</h3>
            <p>${fish.diet}</p>
        </div>
        
        <div class="fish-detail-section">
            <h3>📏 Size</h3>
            <p>${fish.size}</p>
        </div>
        
        <div class="fish-detail-section">
            <h3>⏰ Lifespan</h3>
            <p>${fish.lifespan}</p>
        </div>
        
        <div class="fish-detail-section">
            <h3>🥗 Nutrition</h3>
            <p>${fish.nutrition}</p>
        </div>
        
        <div class="fish-detail-section">
            <h3>🔍 Interesting Facts</h3>
            <ul>
                ${fish.facts.map(fact => `<li>${fact}</li>`).join('')}
            </ul>
        </div>
        
        <div class="fish-detail-section">
            <h3>🌍 Conservation Status</h3>
            <p>${fish.conservation}</p>
        </div>
    `;

    document.getElementById('fishModal').style.display = 'block';
}

// Add CSS for modal content
const modalStyles = `
    .fish-detail-section {
        margin: 1.5rem 0;
    }
    
    .fish-detail-section h3 {
        color: #2c5aa0;
        margin-bottom: 0.5rem;
        font-size: 1.2rem;
    }
    
    .fish-detail-section ul {
        padding-left: 1.5rem;
    }
    
    .fish-detail-section li {
        margin-bottom: 0.5rem;
        line-height: 1.6;
    }
    
    .scientific-name {
        color: #5a7ba8;
        font-size: 1.1rem;
        margin-bottom: 1.5rem;
    }
`;

// Add styles to head
const styleSheet = document.createElement('style');
styleSheet.textContent = modalStyles;
document.head.appendChild(styleSheet);
